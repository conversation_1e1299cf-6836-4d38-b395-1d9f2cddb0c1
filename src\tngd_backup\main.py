#!/usr/bin/env python3
"""
TNGD Backup System - Main Entry Point

Professional backup system with advanced features:
- Resource management and optimization
- Real-time monitoring and progress tracking
- Automatic recovery and checkpoint system
- Comprehensive error handling and retry logic
- OSS storage integration with compression

Usage:
    python -m tngd_backup.main                    # Today's data
    python -m tngd_backup.main 2025-03-26         # Single date
    python -m tngd_backup.main 2025-03-26 2025-03-31  # Date range
"""

import sys
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tngd_backup.core.backup_engine import BackupEngine
from tngd_backup.utils.monitoring import SystemHealthChe<PERSON>


def parse_dates(date_args: List[str]) -> List[datetime]:
    """
    Parse command line date arguments.

    Args:
        date_args: List of date strings from command line

    Returns:
        List of datetime objects representing the backup dates
    """
    if not date_args:
        # Default to today if no arguments provided
        return [datetime.now()]

    dates = []
    for date_str in date_args:
        try:
            # Support multiple date formats
            for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y%m%d']:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    dates.append(parsed_date)
                    break
                except ValueError:
                    continue
            else:
                # If no format worked, try to parse as relative date
                if date_str.lower() == 'today':
                    dates.append(datetime.now())
                elif date_str.lower() == 'yesterday':
                    dates.append(datetime.now() - timedelta(days=1))
                else:
                    print(f"Warning: Could not parse date '{date_str}', skipping")
        except Exception as e:
            print(f"Error parsing date '{date_str}': {e}")

    # If no valid dates were parsed, default to today
    if not dates:
        dates = [datetime.now()]

    return dates


def check_system_readiness() -> bool:
    """
    Check if system is ready for backup operations.
    
    Returns:
        bool: True if system is ready
    """
    health_checker = SystemHealthChecker()
    health_check = health_checker.check_backup_readiness()
    
    if health_check["errors"]:
        print("ERROR: System readiness check failed:")
        for error in health_check["errors"]:
            print(f"   - {error}")
        return False

    if health_check["warnings"]:
        print("WARNING: System warnings:")
        for warning in health_check["warnings"]:
            print(f"   - {warning}")

        # Ask user if they want to continue
        response = input("Continue with backup? (y/N): ").lower().strip()
        if response not in ['y', 'yes']:
            return False

    if health_check["recommendations"]:
        print("RECOMMENDATIONS:")
        for rec in health_check["recommendations"]:
            print(f"   - {rec}")
        print()
    
    return True


def show_usage():
    """Show detailed usage information."""
    print("""
TNGD Backup System v2.0
========================

A professional backup system with advanced resource management,
monitoring, and recovery capabilities.

The system performs date-specific backup for the specified dates.

Usage:
    python -m tngd_backup.main [OPTIONS] [DATES]

Options:
    -h, --help                  Show this help message
    --config PATH               Configuration file path
    --check-only                Only check system readiness
    --verbose                   Enable verbose logging

Examples:
    python -m tngd_backup.main
    python -m tngd_backup.main --config config/production.json
    python -m tngd_backup.main --production
    python -m tngd_backup.main --developer

Features:
    ✓ Thread management (max 4 threads)
    ✓ Memory optimization (1500MB limit)
    ✓ Automatic retry and recovery
    ✓ Real-time progress monitoring
    ✓ Checkpoint system for resume
    ✓ Resource monitoring and alerts
    ✓ Comprehensive error handling
    ✓ OSS storage with compression
    ✓ Date-specific backup with flexible date formats

For more information, see docs/usage.md
""")


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        description="TNGD Backup System v2.0 - Date-Specific Backup",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                           # Backup today's data
  %(prog)s --production              # Backup today's data (production config)
  %(prog)s --developer               # Backup today's data (developer config)
  %(prog)s --production 1/7/2025     # Backup specific date
  %(prog)s 2025-07-01 2025-07-02     # Backup multiple dates
        """
    )
    parser.add_argument(
        '--config',
        help='Configuration file path'
    )
    parser.add_argument(
        '--production',
        action='store_true',
        help='Use production configuration (all tables, optimized settings)'
    )
    parser.add_argument(
        '--developer',
        action='store_true',
        help='Use developer configuration (subset of tables, faster testing)'
    )
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='Only check system readiness'
    )
    # Note: Removed --dry-run parameter as it was not fully implemented
    # Only displayed messages but didn't actually simulate the backup process
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    parser.add_argument(
        '--usage',
        action='store_true',
        help='Show detailed usage information'
    )
    parser.add_argument(
        'dates',
        nargs='*',
        help='Dates to backup (YYYY-MM-DD, DD/MM/YYYY, or relative like "today"). If none provided, backs up today\'s data.'
    )

    return parser


def setup_logging(verbose: bool) -> None:
    """Setup logging configuration."""
    if verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)


def validate_system_and_dates(args) -> tuple:
    """Validate system readiness and prepare backup run."""
    # Validate configuration arguments
    if args.production and args.developer:
        print("ERROR: Cannot specify both --production and --developer flags")
        return False, None

    # Check system readiness
    if not check_system_readiness():
        print("ERROR: System not ready for backup operations")
        return False, None

    if args.check_only:
        print("SUCCESS: System ready for backup operations")
        return True, None

    # Parse dates for backup
    backup_run = parse_dates(args.dates)
    return True, backup_run


def display_backup_info(backup_run: List[datetime], args) -> None:
    """Display backup information to user."""
    print("TNGD Backup System v2.0")
    print("=" * 40)
    print("Mode: DATE-SPECIFIC BACKUP")
    date_list = [d.strftime('%Y-%m-%d') for d in backup_run]
    print(f"Target Dates: {', '.join(date_list)}")
    print(f"Backup Run ID: {backup_run[0].strftime('%Y%m%d_%H%M%S')}")

    if args.config:
        print(f"Config: {args.config}")

    print()


def execute_backup_workflow(backup_run: List[datetime], args) -> int:
    """Execute the backup workflow and return exit code."""
    # Determine configuration file based on flags
    config_file = args.config
    if args.production:
        config_file = "config/production.json"
        print("PRODUCTION: Using production configuration")
    elif args.developer:
        config_file = "config/development.json"
        print("DEVELOPER: Using developer configuration")
    elif not config_file:
        config_file = "config/default.json"
        print("DEFAULT: Using default configuration")

    # Create and run backup engine
    engine = BackupEngine(backup_run, config_file)

    # Run backup
    results = engine.run_backup()

    # Show results
    print("\n" + "=" * 50)
    if results['status'] == 'completed':
        print("SUCCESS: Date-specific backup finished!")
        print(f"Total operations: {results['metrics']['total_tables']}")
        print(f"Completed: {results['metrics']['completed_tables']}")
        print(f"Failed: {results['metrics']['failed_tables']}")
        print(f"Total rows: {results['metrics']['total_rows']:,}")
        print(f"Duration: {results['metrics']['total_duration']/60:.1f} minutes")
        return 0
    else:
        print("ERROR: Backup failed!")
        if results.get('error'):
            print(f"Error: {results['error']}")
        return 1


def main():
    """Main entry point for TNGD backup system."""
    parser = create_argument_parser()
    args = parser.parse_args()

    # Show usage if requested
    if args.usage:
        show_usage()
        return 0

    # Setup logging
    setup_logging(args.verbose)

    try:
        # Validate system and prepare backup run
        is_valid, backup_run = validate_system_and_dates(args)
        if not is_valid:
            return 1

        # Handle check-only mode
        if args.check_only:
            return 0

        # Display backup information
        display_backup_info(backup_run, args)

        # Execute backup workflow
        return execute_backup_workflow(backup_run, args)

    except KeyboardInterrupt:
        print("\nBackup interrupted by user")
        return 1
    except Exception as unexpected_error:
        print(f"Unexpected error: {unexpected_error}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
